# read_cookie.py
import re
import os
import subprocess
import time
from datetime import datetime

class ReadCookie(object):
    """
    启动cookie_extractor.py和解析cookie文件
    """

    def __init__(self, outfile="wechat_keys.txt"):
        self.outfile = outfile

    def parse_cookie(self):
        """
        解析cookie文件，提取最新的appmsg_token、biz、cookie_str和headers
        :return: appmsg_token, biz, cookie_str, headers
        """
        if not os.path.exists(self.outfile):
            print(f"文件 {self.outfile} 不存在")
            return None, None, None, None

        with open(self.outfile, 'r', encoding='utf-8') as f:
            content = f.read()

        # 按分隔符分割，获取最后一个完整的记录
        records = content.split('=' * 60)

        appmsg_token = None
        biz = None
        cookie_str = None
        headers = {}

        # 从最后一个记录开始查找
        for record in reversed(records):
            if 'Cookies:' in record and 'allurl:' in record:
                lines = record.strip().split('\n')

                url_line = None
                cookie_line = None
                headers_section = False

                for line in lines:
                    if line.startswith('allurl:'):
                        url_line = line
                    elif line.startswith('Cookies:'):
                        cookie_line = line
                    elif line.startswith('Headers:'):
                        headers_section = True
                    elif headers_section and line.strip().startswith('  '):
                        # 解析headers行，格式为 "  header_name: header_value"
                        header_match = re.match(r'\s+([^:]+):\s*(.+)', line)
                        if header_match:
                            header_name = header_match.group(1).strip()
                            header_value = header_match.group(2).strip()
                            headers[header_name] = header_value

                if url_line and cookie_line:
                    # 从URL中提取__biz
                    url = url_line.split('allurl:', 1)[1].strip()

                    # 提取__biz
                    biz_match = re.search(r'__biz=([^&]+)', url)
                    if biz_match:
                        biz = biz_match.group(1)

                    # 提取cookie字符串
                    cookie_str = cookie_line.split('Cookies:', 1)[1].strip()

                    # 从cookie字符串中提取appmsg_token
                    appmsg_token_match = re.search(r'appmsg_token=([^;]+)', cookie_str)
                    if appmsg_token_match:
                        appmsg_token = appmsg_token_match.group(1)

                    # 如果找到了有效的数据，就返回
                    if appmsg_token and biz and cookie_str:
                        print(f"找到有效数据:")
                        print(f"  __biz: {biz}")
                        print(f"  appmsg_token: {appmsg_token}")
                        print(f"  cookie长度: {len(cookie_str)}")
                        print(f"  headers数量: {len(headers)}")
                        return appmsg_token, biz, cookie_str, headers
                    else:
                        print(f"数据不完整:")
                        print(f"  __biz: {biz}")
                        print(f"  appmsg_token: {appmsg_token}")
                        print(f"  cookie_str存在: {bool(cookie_str)}")

        print("未找到有效的cookie数据")
        print("调试信息:")
        print(f"文件内容长度: {len(content)}")
        print(f"记录数量: {len(records)}")
        return None, None, None, None

    def start_cookie_extractor(self, timeout=60):
        """
        启动cookie_extractor.py进行cookie抓取
        :param timeout: 抓取超时时间（秒）
        :return: 是否成功启动
        """
        try:
            # 获取当前文件路径
            current_path = os.path.dirname(os.path.realpath(__file__))
            extractor_path = os.path.join(current_path, 'cookie_extractor.py')
            
            if not os.path.exists(extractor_path):
                print(f"未找到cookie_extractor.py文件: {extractor_path}")
                return False
            
            # 构造mitmdump命令
            command = f"mitmdump -s {extractor_path} --listen-port 8080"
            
            print(f"启动命令: {command}")
            print("正在启动cookie抓取器...")
            print("请在浏览器中访问微信公众号文章，程序将自动抓取cookie")
            print(f"抓取将在{timeout}秒后自动停止，或者按Ctrl+C手动停止")
            
            # 启动mitmdump进程
            process = subprocess.Popen(command, shell=True)
            
            # 等待指定时间或手动停止
            try:
                process.wait(timeout=timeout)
            except subprocess.TimeoutExpired:
                print(f"\n{timeout}秒已到，停止抓取...")
                process.terminate()
                process.wait()
            except KeyboardInterrupt:
                print("\n手动停止抓取...")
                process.terminate()
                process.wait()
            
            return True
            
        except Exception as e:
            print(f"启动cookie抓取器失败: {e}")
            return False

    def get_latest_cookies(self):
        """
        获取最新的cookie信息
        :return: dict包含appmsg_token, biz, cookie_str, headers
        """
        appmsg_token, biz, cookie_str, headers = self.parse_cookie()

        if appmsg_token and biz and cookie_str:
            return {
                'appmsg_token': appmsg_token,
                'biz': biz,
                'cookie_str': cookie_str,
                'headers': headers,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        else:
            return None

def main():
    """主函数，演示如何使用"""
    rc = ReadCookie()
    
    print("=== 微信Cookie抓取器 ===")
    print("1. 自动启动抓取")
    print("2. 只解析现有文件")
    
    choice = input("请选择操作(1/2): ").strip()
    
    if choice == '1':
        # 启动抓取器
        if rc.start_cookie_extractor(timeout=120):  # 2分钟超时
            print("\n抓取完成，开始解析...")
            time.sleep(1)  # 等待文件写入完成
        else:
            print("抓取器启动失败")
            return
    
    # 解析cookie
    result = rc.get_latest_cookies()
    
    if result:
        print("\n" + "="*50)
        print("解析结果:")
        print(f"appmsg_token: {result['appmsg_token']}")
        print(f"biz: {result['biz']}")
        print(f"cookie: {result['cookie_str']}")
        print(f"解析时间: {result['timestamp']}")
        print("="*50)
    else:
        print("未找到有效的cookie数据，请确保:")
        print("1. 已正确访问微信公众号文章")
        print("2. 代理设置正确(127.0.0.1:8080)")
        print("3. wechat_keys.txt文件中有有效数据")

if __name__ == '__main__':
    main()
