# 微信公众号阅读量抓取使用说明

## 概述

本系统已经修改为参考 `spider_readnum.py` 的成功实现，能够正确获取微信公众号文章的阅读量、点赞数、分享数等统计信息。

## 修改内容

### 1. 增强的抓包工具 (`cookie_extractor.py`)
- 新增获取关键请求头参数（`x-wechat-key`、`x-wechat-uin`、`exportkey` 等）
- 将这些参数保存到 `wechat_keys.txt` 文件中
- 提供更完整的认证信息

### 2. 改进的Cookie解析器 (`read_cookie.py`)
- 支持解析新增的Headers信息
- 返回完整的认证数据结构
- 兼容原有的数据格式

### 3. 优化的批量爬虫 (`batch_readnum_spider.py`)
- 参考 `spider_readnum.py` 的成功实现
- 使用正确的请求方式和参数
- 采用成功验证的正则表达式提取数据
- 自动使用抓包获取的真实请求头

## 使用步骤

### 第一步：启动抓包工具

```bash
# 启动mitmdump抓包
mitmdump -s cookie_extractor.py --listen-port 8080
```

### 第二步：获取认证信息

1. 在浏览器中设置代理为 `127.0.0.1:8080`
2. 访问任意微信公众号文章
3. 抓包工具会自动获取并保存认证信息到 `wechat_keys.txt`
4. 看到 "已捕获到公众号的凭据" 提示后，可以停止抓包（Ctrl+C）

### 第三步：测试功能

```bash
# 运行测试脚本验证功能
python test_readnum_extraction.py
```

### 第四步：批量抓取

```bash
# 运行批量抓取
python batch_readnum_spider.py
```

或者在代码中使用：

```python
from batch_readnum_spider import BatchReadnumSpider

# 创建爬虫实例
spider = BatchReadnumSpider()

# 批量抓取（最近7天，最多3页，每页5篇）
results = spider.batch_crawl_readnum(max_pages=3, articles_per_page=5, days_back=7)

# 保存结果
if results:
    spider.save_to_excel()
    spider.save_to_json()
    spider.print_summary()
```

## 关键改进

### 1. 正确的请求方式
- 使用 `https://mp.weixin.qq.com/s` 作为基础URL
- 从文章URL中提取正确的参数（`__biz`、`mid`、`idx`、`sn` 等）
- 添加必要的参数如 `wx_header=1`

### 2. 真实的请求头
- 使用抓包获取的真实 `x-wechat-key`
- 使用正确的 `x-wechat-uin` 和 `exportkey`
- 保持与成功案例一致的User-Agent

### 3. 准确的数据提取
- 使用 `spider_readnum.py` 中验证成功的正则表达式
- 提取 `var cgiData = {...read_num: '(\d+)'...}` 中的阅读量
- 提取 `window.appmsg_bar_data = {...like_count: '(\d+)'...}` 中的点赞数

## 注意事项

1. **频率控制**：系统内置了智能频率控制，避免触发微信的反爬机制
2. **代理管理**：自动管理系统代理，避免影响其他网络访问
3. **错误处理**：能够识别验证码页面和异常情况
4. **数据保存**：支持Excel和JSON格式导出

## 故障排除

### 问题1：获取不到阅读量
- **原因**：认证信息过期或不完整
- **解决**：重新运行抓包工具获取最新认证信息

### 问题2：遇到验证码页面
- **原因**：访问频率过高
- **解决**：降低抓取频率，增加延迟时间

### 问题3：请求失败
- **原因**：网络问题或参数错误
- **解决**：检查网络连接，确认抓包数据完整

## 文件说明

- `cookie_extractor.py`：抓包工具，获取认证信息
- `read_cookie.py`：Cookie解析器，解析认证数据
- `batch_readnum_spider.py`：批量爬虫主程序
- `spider_readnum.py`：单篇文章成功示例（参考）
- `test_readnum_extraction.py`：功能测试脚本
- `wechat_keys.txt`：存储认证信息的文件

## 技术原理

系统参考了 `spider_readnum.py` 的成功实现，该文件能够正确获取阅读量的关键在于：

1. 使用正确的请求URL和参数
2. 携带完整的认证信息（Cookie、Headers）
3. 使用准确的正则表达式提取数据

通过抓包工具获取这些关键信息，然后在批量爬虫中复用，确保了功能的可靠性。
